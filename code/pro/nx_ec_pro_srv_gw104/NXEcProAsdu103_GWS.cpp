/**********************************************************************
* NXEcProAsdu103_GWS.h         author:yys      date:02/12/2020         
*---------------------------------------------------------------------
*  note: 国网ASDU103报文转换处理-简要故障报告                                                              
*  
**********************************************************************/

#include "NXEcProAsdu103_GWS.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无

TNXEcProAsdu103GWS::~TNXEcProAsdu103GWS()
{

}
*/
/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu103GWS::TNXEcProAsdu103GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu103(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu103GWS");
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN_ASDU103);
	m_nRobotFileCode = 0;
}

/**
* @brief         直接从本地生成结果回应，如初始化配置;
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu103GWS::DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	bool bIsRobot = false;
	if (pBody->vVarData.size() < 105)
	{
		RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令报文长度不足,不进行处理.","TNXEcProAsdu103GWS");
		return EC_PRO_CVT_FAIL;
	}
	//获取返回信息标示符
	m_nRii = (u_int8)(pBody->nRii);
	m_nFun = (u_int8)(pBody->nFun);
	m_nInf = (u_int8)(pBody->nInf);

	//获取召唤的文件名
	char cFileName[255]="";
	char cFilePath[255]="";
	char _cFileName[255]="";
	char _cFileExt[255]="";
	memcpy(cFileName,&(pBody->vVarData[1]),100);

	char *pTmp = NULL;
	string strFullFname = cFileName;
	//厂站模型文件
	pTmp = strstr( (char *)strFullFname.c_str(), "CBSDir" );
	if (NULL != pTmp)//找到相关关键字,属于机器人.
	{
		bIsRobot = true;
		__InitCfgFile();
		if ( 1 == m_nRobotFileCode )
		{
			bool bloadlib_gbk2utf8= m_ZclLibmngr_Gbk2Utf8.load_lib();
			if (!bloadlib_gbk2utf8)
			{
				printf("加载动态库libZclGbkToUtf8失败!!!!\n");
				return false;
			}
		}
	}
	else
	{
		bIsRobot = false;
	}

	//20240102
	char *plogfile = NULL;
	char *psettingfile = NULL;
	//log文件目录
	plogfile = _strstr_nocase((char*)strFullFname.c_str(), "nxlog");
	//定值文件目录
	psettingfile = _strstr_nocase((char*)strFullFname.c_str(), "SettingUp");
	//到通用文件夹下查找相关文件并返回结果
	if (NULL != plogfile)
	{
		//获取指定的起始传输位置
		int nBeginSendPos;
		memcpy(&nBeginSendPos,&(pBody->vVarData[101]),4);
		_REVERSE_BYTE_ORDER_32(nBeginSendPos);

		int npos = strFullFname.find("nxlog");
		string strName = "";
		if (npos != string::npos)
		{
			strName = strFullFname.substr(npos+5);//得到nxlog后面的的路径包括/
		}

		sprintf(cError,"收到召唤日志文件命令,召唤的文件名为:%s,指定的起始位置:%d",cFileName,nBeginSendPos);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		if (_GeneralFileHandle_nxlog((char*)strName.c_str(),nBeginSendPos,lResult,strFullFname) <0 )
		{
			return EC_PRO_CVT_FAIL;
		}
		RcdTrcLogWithParentClass("上送日志文件数据结束.","TNXEcProAsdu103GWS");
	}
	else if (NULL != psettingfile)
	{
		//获取指定的起始传输位置
		int nBeginSendPos;
		memcpy(&nBeginSendPos,&(pBody->vVarData[101]),4);
		_REVERSE_BYTE_ORDER_32(nBeginSendPos);

		int npos = strFullFname.find("SettingUp");
		string strName = "";
		if (npos != string::npos)
		{
			strName = strFullFname.substr(npos+9);//得到SettingUp后面的的路径包括/
		}

		sprintf(cError,"收到召唤定值文件命令,召唤的文件名为:%s,指定的起始位置:%d",cFileName,nBeginSendPos);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		if (_GeneralFileHandle_SettingUp((char*)strName.c_str(),nBeginSendPos,pBody,lResult,strFullFname) <0 )
		{
			return EC_PRO_CVT_FAIL;
		}
		RcdTrcLogWithParentClass("上送定值文件数据结束.","TNXEcProAsdu103GWS");
	}
	else
	{
		if (sy_get_file_name(cFileName,cFilePath,_cFileName,_cFileExt) != 0) //去掉命令中可能的路径信息
		{
			RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令报文中文件名格式有误或者为空.","TNXEcProAsdu103GWS");
			return EC_PRO_CVT_FAIL;
		}  
		memset(cFileName,0,255);
		sprintf(cFileName,"%s.%s",_cFileName,_cFileExt);

		if ( bIsRobot && 1 == m_nRobotFileCode)
		{
			char cRobotFilePaht[255] = "";
			sy_format_file_path( cFilePath, cRobotFilePaht );
			char cRobotFileName[255] = "";
			sprintf( cRobotFileName, "%s%s", cRobotFilePaht, CvtGbkToUtf8( cFileName ) );
			strFullFname = cRobotFileName;
		}

		//获取指定的起始传输位置
		int nBeginSendPos;
		memcpy(&nBeginSendPos,&(pBody->vVarData[101]),4);
		_REVERSE_BYTE_ORDER_32(nBeginSendPos);
		sprintf(cError,"收到召唤通用文件命令,召唤的文件名为:%s,指定的起始位置:%d",cFileName,nBeginSendPos);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");

		//上送文件内容
		if ( _GeneralFileHandle(cFileName,nBeginSendPos,lResult,bIsRobot,strFullFname)<0)
		{
			return EC_PRO_CVT_FAIL;
		}
		RcdTrcLogWithParentClass("上送通用文件数据结束.","TNXEcProAsdu103GWS");
	}
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief		通用文件上送处理函数			
* @param[in]     const char * cFileName:指定的文件名称
* @param[in]    int nBeginSendPos:起始传输位置
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103GWS::_GeneralFileHandle(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN bool &bIsRobot,IN string & strFullFname)
{
	char cError[500]="";

	//获取文件存放的路径-通用文件路径;
	char cTemp[255]="";
	char cGeneralFilePathName[500]="";  //包含路径的文件名
	BASIC_CFG_TB CfgTb;
	SUBSTATION_TB SubCFG;

	if (!m_pModelSeek->GetBasicCfg(CfgTb))
	{
		RcdErrLogWithParentClass("_GeneralFileHandle():获取基本配置数据失败","TNXEcProAsdu103");
		return -1;
	}

	if (sy_format_file_path(CfgTb.str_file_path.c_str(),cTemp) < 0) 
	{
		sprintf(cError,"_GeneralFileHandle():格式化通用文件路径[%s]出错.",CfgTb.str_file_path.c_str());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}
	
	string strStnName = m_pModelSeek->GetSubStationBasicCfg()->str_aliasname;
	if (bIsRobot)//找到相关关键字,属于机器人.
	{
		sprintf(cGeneralFilePathName,"%s%s/%s",cTemp,strStnName.c_str(),strFullFname.c_str());
	}
	else
	{
		sprintf(cGeneralFilePathName,"%s%s%s%s",cTemp,"通用文件",FILE_PATH_OPT_STR,cFileName);
	}

	//判断文件是否存在
	FILE_PROPERTY_INF FileInfo;
	if (sy_get_file_property(cGeneralFilePathName,&FileInfo) != 0)
	{
		sprintf(cError,"通用文件[%s]不存在.",cGeneralFilePathName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");
		_CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); //文件不存在,转成空文件报文.
		return 0;
	}

	//存在则处理
	if ( _CvtFileToFrameBody(cGeneralFilePathName,nBeginSendPos,&FileInfo,lResult) < 0)
	{
		sprintf(cError,"转换通用文件[%s]到规约信息体时出错.",cGeneralFilePathName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}
	return 0;
}

/**
* @brief		日志文件上送处理函数			
* @param[in]     const char * cFileName:指定的文件名称
* @param[in]    int nBeginSendPos:起始传输位置
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103GWS::_GeneralFileHandle_nxlog(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN string & strFullFname)
{
	char cError[500]="";

	//获取文件存放的路径-通用文件路径;
	char cTemp[255]="";
	char cGeneralFilePathName[500]="";  //包含路径的文件名

	BASIC_CFG_TB CfgTb;
	if (!m_pModelSeek->GetBasicCfg(CfgTb)) 
	{
		RcdErrLogWithParentClass("__QueryGeneralFilesList:读取系统基本配置表信息失败。","TNXEcProAsdu101GWS");
		return -1;
	}

	if (sy_format_file_path(CfgTb.str_logroot_path.c_str(), cTemp) < 0) //日志根路径
	{
		sprintf(cError,"_GeneralFileHandle():格式化通用文件路径[%s]出错.",CfgTb.str_file_path.c_str());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}

	sprintf(cGeneralFilePathName,"%s%s",cTemp,cFileName);

	//判断文件是否存在
	FILE_PROPERTY_INF FileInfo;
	if (sy_get_file_property(cGeneralFilePathName,&FileInfo) != 0)
	{
		sprintf(cError,"通用文件[%s]不存在.",cGeneralFilePathName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");
		_CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); //文件不存在,转成空文件报文.
		return 0;
	}

	//存在则处理
	if ( _CvtFileToFrameBody(cGeneralFilePathName,nBeginSendPos,&FileInfo,lResult) < 0)
	{
		sprintf(cError,"转换通用文件[%s]到规约信息体时出错.",cGeneralFilePathName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}
	return 0;
}


/**
* @brief         字符串不区分大小写匹配
* @param[in]     const string & s : 匹配字符串
* @param[in]     const string & s2 :匹配字符串
* @return        
*/
char* TNXEcProAsdu103GWS::_strstr_nocase(const char * str1, const char *  str2)
{
	char *cp = (char*)str1;
	char *s1,*s2;
	//如果传进str2是空,返回str1
	if (!*str2)
	{
		return ((char*)str1);
	}

	while(*cp)
	{
		s1 = cp;
		s2 = (char *)str2;

		while( *s1 && *s2 && toupper(*s1)==toupper(*s2))
		{
			s1++;
			s2++;
		}

		if (!*s2)
		{
			return cp;
		}

		cp++;
	}
	return NULL;
}

//加载配置文档
int TNXEcProAsdu103GWS::__InitCfgFile()
{
	CIniOperate clMyIni;
	if (!clMyIni.SetIniFileName("Gw103.ini")) 
	{
		printf("[TNXEcProAsdu103GWS:__InitCfgFile] 没有找到配置文件'Gw103.ini'!.\n");
	}
	else	
	{
		string strTmp;
		clMyIni.ParseIni();
		clMyIni.GetPrivateProfileStr("SYS","robot_file_code","0",strTmp);

		m_nRobotFileCode = atoi( strTmp.c_str() );

		printf("[TNXEcProAsdu103GWS:__InitCfgFile] 根据配置文档读到的机器人巡视文件的文件名编码格式为:%d.\n",m_nRobotFileCode);
	}	

	return 0;
}

/***********************************************************************
* @brief     	将汉字由Gbk转换为Utf8格式
* @param[in]    cObj 要转换的字符串
* @return        char *
***********************************************************************/
char * TNXEcProAsdu103GWS::CvtUtf8ToGbk(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN_ASDU103);
	m_ZclLibmngr_Gbk2Utf8.convert_utf8_gbk(cObj,m_cChr,MAX_CHAR_BUFF_LEN_ASDU103-1);
	return m_cChr;
}
/***********************************************************************
* @brief     	将汉字由Utf8转换为Gbk格式
* @param[in]    cObj 要转换的字符串
* @return        char *
***********************************************************************/
char * TNXEcProAsdu103GWS::CvtGbkToUtf8(const char * cObj)
{
	memset(m_cChr,0,MAX_CHAR_BUFF_LEN_ASDU103);
	m_ZclLibmngr_Gbk2Utf8.convert_gbk_utf8(cObj,m_cChr,MAX_CHAR_BUFF_LEN_ASDU103-1);
	return m_cChr;
}

/**
* @brief		定值文件上送处理函数
* @param[in]     const char * cFileName:指定的文件名称
* @param[in]    int nBeginSendPos:起始传输位置
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103GWS::_GeneralFileHandle_SettingUp(IN const char * cFileName,IN int nBeginSendPos,IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult,IN string & strFullFname)
{
	char cError[500]="";

	//获取文件存放的路径-定值文件路径;
	char cTemp[255]="";
	char cGeneralFilePathName[500]="";  //包含路径的文件名

	BASIC_CFG_TB CfgTb;
	if (!m_pModelSeek->GetBasicCfg(CfgTb))
	{
		RcdErrLogWithParentClass("_GeneralFileHandle_SettingUp:读取系统基本配置表信息失败。","TNXEcProAsdu103GWS");
		return -1;
	}

	if (sy_format_file_path(CfgTb.str_file_path.c_str(), cTemp) < 0) //文件根路径
	{
		sprintf(cError,"_GeneralFileHandle_SettingUp():格式化定值文件路径[%s]出错.",CfgTb.str_file_path.c_str());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		return -1;
	}

	// 从 cFileName 中解析地址和文件名（格式：/addr/具体文件名）
	// 103需要解析出具体的文件名进行处理
	int nAddr = 0;
	string strFileName = "";
	string strPath = cFileName; // 创建string副本用于解析
	if (strPath.length() > 1 && strPath[0] == '/')
	{
		// 查找第二个 '/' 的位置
		size_t nSecondSlashPos = strPath.find('/', 1);
		if (nSecondSlashPos != string::npos)
		{
			// 提取两个 '/' 之间的地址
			string strAddr = strPath.substr(1, nSecondSlashPos - 1);
			nAddr = atoi(strAddr.c_str());
			// 获取文件名部分（第二个 '/' 之后的内容）
			strFileName = strPath.substr(nSecondSlashPos + 1);

			sprintf(cError,"_GeneralFileHandle_SettingUp:从路径[%s]中解析出地址[%d],文件名[%s]", cFileName, nAddr, strFileName.c_str());
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		}
		else
		{
			sprintf(cError,"_GeneralFileHandle_SettingUp:路径格式错误[%s],无法解析地址", cFileName);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu103GWS");
			return -1;
		}
	}
	else
	{
		sprintf(cError,"_GeneralFileHandle_SettingUp:路径格式错误[%s],应以/开头", cFileName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		return -1;
	}

	// 根据解析出的地址获取对应的IED ID
	const IED_TB* pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nAddr);
	if (pIed == NULL)
	{
		sprintf(cError,"_GeneralFileHandle_SettingUp:根据地址[%d]获取IED配置失败", nAddr);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		return -1;
	}

	// 构建路径：基础路径 + "/SettingUp/" + IED ID + "/" + 文件名
	// 确保路径格式正确，避免双斜杠
	if (cTemp[strlen(cTemp)-1] == '/')
	{
		if (!strFileName.empty())
		{
			sprintf(cGeneralFilePathName,"%sSettingUp/%d/%s",cTemp, pIed->n_obj_id, strFileName.c_str());
		}
		else
		{
			sprintf(cGeneralFilePathName,"%sSettingUp/%d",cTemp, pIed->n_obj_id);
		}
	}
	else
	{
		if (!strFileName.empty())
		{
			sprintf(cGeneralFilePathName,"%s/SettingUp/%d/%s",cTemp, pIed->n_obj_id, strFileName.c_str());
		}
		else
		{
			sprintf(cGeneralFilePathName,"%s/SettingUp/%d",cTemp, pIed->n_obj_id);
		}
	}

	sprintf(cError,"_GeneralFileHandle_SettingUp:构建路径[%s],解析地址[%d],IED ID[%d]", cGeneralFilePathName, nAddr, pIed->n_obj_id);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");

	//判断文件是否存在
	FILE_PROPERTY_INF FileInfo;
	if (sy_get_file_property(cGeneralFilePathName,&FileInfo) != 0)
	{
		sprintf(cError,"定值文件[%s]不存在.",cGeneralFilePathName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		_CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); //文件不存在,转成空文件报文.
		return 0;
	}

	//存在则处理
	if ( _CvtFileToFrameBody(cGeneralFilePathName,nBeginSendPos,&FileInfo,lResult) < 0)
	{
		sprintf(cError,"转换定值文件[%s]到规约信息体时出错.",cGeneralFilePathName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103GWS");
		return -1;
	}
	return 0;
}