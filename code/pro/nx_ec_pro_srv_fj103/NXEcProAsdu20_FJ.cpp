/**********************************************************************
* NXEcProAsdu20_FJ.cpp         author:ml     date:2025/01/12            
*---------------------------------------------------------------------
*  note: 福建103 ASDU20报文转换处理实现文件 - 远方复归二次设备指示灯                                                             
*  
**********************************************************************/

#include "NXEcProAsdu20_FJ.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu20FJ::~TNXEcProAsdu20FJ()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu20FJ::TNXEcProAsdu20FJ(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu20FJ");
	m_pCommonMsg = NULL;
}

/**
* @brief         转换规约信息到NX通用消息结构
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
* @return        >=0:成功 <0:失败
*/
int TNXEcProAsdu20FJ::ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
    
	// 解析报文中的装置地址，按福建103规范：高8位为103地址，0=子站复归，255=全站复归，1-254=单装置复归
	PRO_FRAME_BODY_LIST::iterator iteCmd = pBodyList->begin();
	while(iteCmd != pBodyList->end())
	{
		if ( iteCmd->nType == 0x14 && iteCmd->nCot == 0x14)  // 复归命令识别
		{
			// 检查FUN和INF是否符合福建103规范
			if (iteCmd->nFun != 0xFF || iteCmd->nInf != 0x13)
			{
				sprintf(cError,"ConvertProToCommonMsg():复归命令格式错误,FUN=%02X,INF=%02X(期望FFH,13H)",
					iteCmd->nFun, iteCmd->nInf);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu20FJ");
				++iteCmd;
				continue;
			}
            
			u_int8 nHighByte = (iteCmd->nAddr >> 8) & 0xFF;  // 福建103：ASDU地址高8位
			
			sprintf(cError,"ConvertProToCommonMsg():收到ASDU20复归命令,ASDU地址=%04X,高8位=%d,RII=%d",
				iteCmd->nAddr, nHighByte, iteCmd->nRii);
			RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
            
			if ( nHighByte == 0 )  // 子站本身复归
			{
				RcdTrcLogWithParentClass("ConvertProToCommonMsg():子站本身复归","TNXEcProAsdu20FJ");
				_CvtWholeStationReset(iteCmd->nRii, lMsg);
			}
			else if ( nHighByte == 255 )  // 全站复归
			{
				RcdTrcLogWithParentClass("ConvertProToCommonMsg():全站复归","TNXEcProAsdu20FJ");
				_CvtWholeStationReset(iteCmd->nRii, lMsg);
			}
			else if ( nHighByte >= 1 && nHighByte <= 254 )  // 单装置复归
			{
				sprintf(cError,"ConvertProToCommonMsg():单装置复归,103地址=%d",nHighByte);
				RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
				_CvtOneDevReset(iteCmd->nAddr, iteCmd->nRii, lMsg);
			}
			else
			{
				sprintf(cError,"ConvertProToCommonMsg():无效的ASDU地址高8位=%d",nHighByte);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu20FJ");
			}
		}
		iteCmd++;
	}
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu20FJ::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	int nRet = EC_PRO_CVT_FAIL;
	m_pCommonMsg = pMsg;
    
	switch(pMsg->n_msg_type)
	{
	case NX_IED_CTRL_IEDTRIP_REST_REP:  // 复归命令回应
		sprintf(cError,"ConvertCommonMsgToPro():收到复归命令回应,result=%d",pMsg->n_result);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
		nRet = _CvtResetResultToPro(lCmd, lResult);
		break;
	default:
		sprintf(cError,"ConvertCommonMsgToPro()中暂不支持n_msg_type=%d的消息处理",pMsg->n_msg_type);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu20FJ");
		nRet = EC_PRO_CVT_NOSUPPORT;
		break;
	}
    
	m_pCommonMsg = NULL;
	return nRet;
}

/**
* @brief         全站复归处理
* @param[in]     u_int8 nRii :返回信息标识符
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
*/
void TNXEcProAsdu20FJ::_CvtWholeStationReset(IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg)
{
	RcdTrcLogWithParentClass("_CvtWholeStationReset():收到全站设备复归命令","TNXEcProAsdu20FJ");

	LIST_IED ListIed;
	m_pModelSeek->GetAllIedStatus(ListIed);
	LIST_IED::iterator iteIed = ListIed.begin();
	int nDevCount = 0;
	
	while(iteIed != ListIed.end())
	{
		if ( iteIed->e_psrtype != SUB_STATION) // 排除子站本身
		{
			if (iteIed->e_opramode == OPRAMODE_RUN) // 只针对运行状态的设备
			{
				_MakeResetCommonMsg(iteIed->n_obj_id, nRii, lMsg);
				nDevCount++;
			}
		}
		iteIed++;
	}
    
	char cError[255] = "";
	sprintf(cError,"_CvtWholeStationReset():生成全站复归命令完成,共%d个设备",nDevCount);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
}

/**
* @brief         单装置复归处理
* @param[in]     u_int16 nAsduAddr :ASDU地址（高8位为103地址）
* @param[in]     u_int8 nRii :返回信息标识符
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
*/
void TNXEcProAsdu20FJ::_CvtOneDevReset(IN u_int16 nAsduAddr, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg)
{
	char cError[255] = "";
	u_int8 nAddr103 = (nAsduAddr >> 8) & 0xFF;  // 福建103：ASDU地址高8位为103地址
	
	sprintf(cError,"_CvtOneDevReset():收到设备103地址为%d的复归命令,RII=%d",nAddr103,nRii);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");

	const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nAddr103);
	if (pIed == NULL)
	{
		sprintf(cError,"_CvtOneDevReset():未找到103地址为%d的装置配置",nAddr103);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu20FJ");
		return;
	}

	if (pIed->e_opramode == OPRAMODE_RUN) // 只对运行状态的设备进行复归
	{
		_MakeResetCommonMsg(pIed->n_obj_id, nRii, lMsg);
		sprintf(cError,"_CvtOneDevReset():为设备(IED_ID=%d,103地址=%d)生成复归命令",
			pIed->n_obj_id, nAddr103);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
	}
	else
	{
		sprintf(cError,"_CvtOneDevReset():设备(IED_ID=%d,103地址=%d)非运行状态,跳过复归",
			pIed->n_obj_id, nAddr103);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
	}
}

/**
* @brief         生成设备复归通用消息
* @param[in]     int nIedId :设备ID
* @param[in]     u_int8 nRii :返回信息标识符
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 生成的通用消息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu20FJ::_MakeResetCommonMsg(IN int nIedId, IN u_int8 nRii, OUT NX_COMMON_MSG_LIST & lMsg)
{
	char cError[255] = "";
	NX_COMMON_MESSAGE CommonMsg;
    
	// 初始化通用消息结构
	_init_common_msg_struct(CommonMsg);
	
	// 填充通用消息 - 使用复归专用命令类型
	CommonMsg.n_msg_topic = NX_TOPIC_COMMAND;
	CommonMsg.n_msg_type = NX_IED_CTRL_IEDTRIP_REST_ASK;  // 复归命令类型
	CommonMsg.n_obj_id = nIedId;
	CommonMsg.n_obj_type = NX_OBJ_TYPE_NX_IED;
	CommonMsg.n_sub_obj_id = 0;
	CommonMsg.n_data_src = 2;  // 表示命令来自远方主站
	CommonMsg.b_lastmsg = true;
	
	// 使用backup字段保存RII信息
	CommonMsg.n_backup = nRii;
	
	// 关键：设置invoke_id保存RII用于回应匹配（参考现有invoke_id机制）
	sprintf(CommonMsg.c_invoke_id, "RESET_RII_%d", nRii);
	
	lMsg.push_back(CommonMsg);
    
	sprintf(cError,"_MakeResetCommonMsg():为设备(IED_ID=%d)生成复归通用消息,RII=%d",nIedId,nRii);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
	
	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         复归结果转换为规约回应报文
* @param[in]     PRO_FRAME_BODY_LIST & lCmd:原命令列表
* @param[out]    PRO_FRAME_BODY_LIST & lResult :生成的回应报文列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu20FJ::_CvtResetResultToPro(IN PRO_FRAME_BODY_LIST & lCmd, OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	PRO_FRAME_BODY ResponseBody;
    
	// 检查原命令列表
	if (lCmd.empty()) {
		RcdErrLogWithParentClass("_CvtResetResultToPro():原命令列表为空","TNXEcProAsdu20FJ");
		return EC_PRO_CVT_FAIL;
	}
	
	PRO_FRAME_BODY OrigCmd = lCmd.front();
    
	// 获取变电站配置
	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
	if (pStation == NULL) {
		RcdErrLogWithParentClass("_CvtResetResultToPro():获取变电站配置失败","TNXEcProAsdu20FJ");
		return EC_PRO_CVT_FAIL;
	}
    
	// 构造ASDU1回应报文 - 严格按照福建103规范
	ResponseBody.nType = 0x01;  // ASDU1
	ResponseBody.nVsq = 0x81;
	ResponseBody.nSubstationAdd = pStation->n_outaddr103;
	ResponseBody.nAddr = OrigCmd.nAddr;  // 保持原ASDU地址
	ResponseBody.nCpu = 0;
	ResponseBody.nZone = 0;
	ResponseBody.nFun = 0xFF;
	ResponseBody.nInf = 0x13;
	ResponseBody.nRii = OrigCmd.nRii;  // 保持原RII
    
	// 根据真实执行结果设置COT和DPI（关键：基于m_pCommonMsg->n_result）
	int nOprResult = ((m_pCommonMsg->n_result == 0) ? 0 : 1);  // 0 success; other failed;
	
	if (nOprResult == 0) {
		ResponseBody.nCot = 0x14;  // 成功 - 肯定认可
	} else {
		ResponseBody.nCot = 0x15;  // 失败 - 否定认可  
	}
    
	// 构造可变数据部分
	ResponseBody.vVarData.clear();
	
	// 添加DPI值
	if (nOprResult == 0) {
		ResponseBody.vVarData.push_back(2);  // DPI=确定（有效）
	} else {
		ResponseBody.vVarData.push_back(1);  // DPI=不确定（无效）
	}
    
	// 添加7字节时标CP56Time2a
	time_t nCurrentTime = time(NULL);
	CTimeConvert TimeCvt(nCurrentTime, 0);
	string strTime;
	TimeCvt.GetCP56TIMe(strTime);
	ResponseBody.vVarData.insert(ResponseBody.vVarData.end(), strTime.begin(), strTime.end());
	
	// 添加附加信息SIN (原命令的RII) - 关键用于主站匹配
	ResponseBody.vVarData.push_back(OrigCmd.nRii);
	
	lResult.push_back(ResponseBody);
    
	sprintf(cError,"_CvtResetResultToPro():生成复归回应成功,COT=%02X,DPI=%d,RII=%d,执行结果=%d",
		ResponseBody.nCot, (nOprResult == 0) ? 2 : 1, OrigCmd.nRii, m_pCommonMsg->n_result);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu20FJ");
	
	return EC_PRO_CVT_SUCCESS;
}
