/**********************************************************************
* NXEcSrvProOperation.cpp         author:jjl      date:29/11/2013            
*---------------------------------------------------------------------
*  note:服务端规约业务处理类实现文件                                                              
*  
**********************************************************************/

#include "NXEcSrvProOperation.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXEcSrvProOperation::~CNXEcSrvProOperation()
{
}

/**
* @brief         构造函数 
* @param[in]     const PRO_OPERATION_PARAM * pParam:规约业务参数指针
* @param[out]    无
* @return        无
*/
CNXEcSrvProOperation::CNXEcSrvProOperation(IN const PRO_OPERATION_PARAM * pParam)
	:TNXEcProOperation(pParam->pLogRecord)
{
	m_nPackegId = 0;
	if( pParam != NULL )
	{
		m_pProParam = (SRV_PRO_START_PARAM *)pParam->pProStartParam;
		if( m_pProParam != NULL )
		{
			m_pNotOrderDevList = m_pProParam->pNotOrderDevList;
		}
		m_pMsgOperaObj    = pParam->pNxMsgOperaObj;
		m_pModelSeekIns   = pParam->pModelSeekIns;
		m_pProCvtFactory  = pParam->pProCvtFactoryIns;
		m_pProTransObj    = pParam->pProTransObj;
		m_pProExplainFactory = pParam->pProExplainFactoryIns;
	}
	
	// 设置类名称
	_SetLogClassName("CNXEcSrvProOperation");
}

/**
* @brief         初始化业务相关线程信息
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvProOperation::_InitThreadInfo()
{
	char cError[255]="";

	// 初始化召唤命令处理的线程池
	int i ;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		m_CallPoolParam[i].nIndex = i;
		m_CallPoolParam[i].bFree  = true;
		m_CallPoolParam[i].bThreadStart = false;
		m_CallPoolParam[i].pReserve = NULL;
		m_CallPoolParam[i].bExit = true;
	}

	// 首次增加3个线程到线程管理队列，但不启动
	if( !_StartProCmdThreadPool(THREAD_POOL_INIT_START_NUM,false) )
	{
		return false;
	}

	// 初始化公共线程
	bool bRet = _InitPublicThreadInfo();
	return bRet;
}

/**
* @brief         释放资源
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvProOperation::ReleaseSource()
{
	char cError[255]="";
	
	// 退出标识置位
	m_bEnd = true;

	int i =0;
	// 等待线程退出
	int n=0;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart )
		{
			sy_unname_sem_post(&m_CallPoolParam[i].hSem);  // 激活线程
			n= 0;
			while( !m_CallPoolParam[i].bExit ) // 确认是否退出
			{
				_ZERO_MEM(cError,255);
				sprintf(cError,"index=%d的召唤命令处理线程退出标识=%d,等待...",i,m_CallPoolParam[i].bExit);
				RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
				sy_sleep(500);
				n++;
				if( n > 10)   // 最多等待5秒
				{
					sprintf(cError,"index=%d的召唤命令处理线程%d秒内未退出,不再等待",i,n*500/1000);
					RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
					break;
				}
				sy_unname_sem_post(&m_CallPoolParam[i].hSem); // 再发送信号
			}
			// 销毁信号
			sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
			m_CallPoolParam[i].bThreadStart = false;

			_ZERO_MEM(cError,255);
			sprintf(cError,"销毁index=%d的召唤命令处理线程信号量",i);
			RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		}
	}

	// 停止线程
	StopAllThread();
	
	// 清除事件队列
	NX_EVENT_MESSAGE Msg;
	while (m_NxEventDeque.size() > 0 )
	{
		Msg = m_NxEventDeque.front();
		Msg.list_subfields.clear();
		m_NxEventDeque.pop_front();
	}

	// 规约命令队列
	PRO_FRAME_BODY ProBody;
	while ( m_ProCmdDeque.size() > 0 )
	{
		ProBody = m_ProCmdDeque.front();
		ProBody.vVarData.clear();
		m_ProCmdDeque.pop_front();
	}

	// 清除请求/结果队列
	CAutoLockOnStack tmpLock(&m_LockForPackegDeque);
	ASK_RES_MATCH_PACKAGE tmpPack;
	while ( m_AskResPackegDeque.size() > 0 )
	{
		tmpPack = m_AskResPackegDeque.front();
		_ClearAskResPackeg(tmpPack);
		m_AskResPackegDeque.pop_front();
	}

	RcdTrcLogWithParentClass("ReleaseSource():停止线程并释放资源完毕","CNXEcSrvProOperation");
	return true;
}

/**
* @brief         初始化规约传输对象
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvProOperation::_DoInitProTransObj()
{
	if( m_pProTransObj != NULL )
	{
		m_pProTransObj->SetLinkStatusChgCalBak(this,(PFUNC_ON_CHANNEL_STATUS_CHG)_OnChannelStatusChgRecv);
		m_pProTransObj->SetProDataSendResultCalBak(this,(PFUNC_ON_PRO_DATA_SEND_RESULT)_OnProFrameSendResultRecv);
		m_pProTransObj->SetRecvProDataCalBak(this,(PFUNC_ON_RECV_PRO_DATA)_OnRecvProFrameData);
		RcdTrcLogWithParentClass("_DoInitProTransObj():初始化设置规约传输对象回调完毕","CNXEcSrvProOperation");
		return true;
	}

	RcdErrLogWithParentClass("_DoInitProTransObj():规约传输对象为NULL,无法初始化","CNXEcSrvProOperation");
	return false;
}


/**
* @brief         初始化NX消息处理业务对象
* @param[in]     无
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvProOperation::_DoInitMsgOperaObj()
{
	if( m_pMsgOperaObj != NULL )
	{
		m_pMsgOperaObj->SetRecvCommonMsgCalBak(this,(PFUNC_ON_NXCOMMON_MSG_HANDLE)_OnRecvCommonMsgRes);
		m_pMsgOperaObj->SetRecvEventMsgCalBak(this,(PFUNC_ON_NXEVENT_MSG_HANDLE)_OnRecvEventMsg);
		RcdTrcLogWithParentClass("_DoInitMsgOperaObj():设置NX消息业务对象回调函数完毕","CNXEcSrvProOperation");
		return true;
	}

	RcdErrLogWithParentClass("_DoInitMsgOperaObj():NX消息业务对象为NULL,无法初始化","CNXEcSrvProOperation");
	return false;
}

/**
* @brief         召唤结果处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__DoCallResponseLoop()
{
	INXEcProConvertObj *pProCvtObj = __CreateOneCvtObj();
	if( pProCvtObj == NULL )
	{
		RcdErrLogWithParentClass("__DoCallResponseLoop():创建转换对象失败,退出召唤结果处理线程","CNXEcSrvProOperation");
		return -1;
	}

	while ( !m_bEnd )
	{
		__CallResponseHandle(pProCvtObj);

		sy_sleep(100);
	}

	delete pProCvtObj;
	RcdTrcLogWithParentClass("__DoCallResponseLoop()正常退出","CNXEcSrvProOperation");
	return 0;
}

/**
* @brief         召唤回应结果处理
* @param[in]     INXEcProConvertObj * pProCvtObj:转换对象
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__CallResponseHandle(IN INXEcProConvertObj * pProCvtObj)
{
	// 锁定队列
	CAutoLockOnStack tmpLock(&m_LockForPackegDeque);

	ASK_RES_MATCH_PAK_DEQUE::iterator itePackeg = m_AskResPackegDeque.begin();

	while( itePackeg != m_AskResPackegDeque.end() )
	{
		// 判断当前的链路状态,如果已断开则清除所有命令
		if( m_nCurLinkStatus == COMMU_STATUS_OUTLINE )
		{
			_ClearAskResPackeg(*itePackeg);
			// 删除该结点,并获取下个结点的位置
			itePackeg = m_AskResPackegDeque.erase(itePackeg);  
			RcdErrLogWithParentClass("__CallResponseHandle():与对端连接断开,清除未完成命令","CNXEcSrvProtcol");
			continue;
		}

		// 控制命令的转换处理：为控制命令、收到结束帧且还没有转换为NX通用消息命令的
		if(( itePackeg->bCtrlCmd )&&( itePackeg->bRcvLastCmd )&&( itePackeg->NxCmdMap.size() <= 0) )
		{
			if( !__PackegCtrlCmdHandle((*itePackeg),pProCvtObj) )  // 处理失败,则删除该结点
			{
				_ClearAskResPackeg(*itePackeg);
				// 删除该结点,并获取下个结点的位置
				itePackeg = m_AskResPackegDeque.erase(itePackeg);  
				RcdErrLogWithParentClass("__CallResponseHandle():控制命令处理失败,清除该命令","CNXEcSrvProtcol");
			}
			else // 成功，则处理下个信息包
			{
				++itePackeg;
			}
			continue;
		}

		// 结果的转换处理 (确认NX命令映射表中是否所有命令都收到结果,若收到则转换为规约并删除该结点，否则等待)
		if( __PackegResultHandle((*itePackeg),pProCvtObj) )
		{
			// 清空信息包内容
			_ClearAskResPackeg( (*itePackeg) );
			// 删除该结点,并获取下个结点的位置
			itePackeg = m_AskResPackegDeque.erase(itePackeg);  
			continue;
		}

		// 超时处理(对于超时命令,直接生成失败回应)
		__PackegTimeOutHandle(*itePackeg);

		itePackeg ++;
	} // while( itePackeg != m_AskResPackegDeque.end() )

	return 0;
}

/**
* @brief         命令/结果匹配队列中规约控制命令收全后进行处理
* @param[in]     ASK_RES_MATCH_PACKAGE & Packeg:命令信息包
* @param[in]     INXEcProConvertObj * pProCvtObj;规约转换对象指针
* @return        bool :true-成功 false-失败
*/
bool CNXEcSrvProOperation::__PackegCtrlCmdHandle(IN ASK_RES_MATCH_PACKAGE & Packeg,IN IN INXEcProConvertObj * pProCvtObj)
{
	char cError[510] = "";
	NX_COMMON_MSG_LIST NxCmdList;
	PRO_FRAME_BODY_LIST ProResultList;

	if( pProCvtObj == NULL )
		return false;

	// 将控制命令转换NX命令
	if(pProCvtObj->ConvertProToCommonMsg(&Packeg.ProCmdList,NxCmdList,ProResultList) >= 0)
	{
		sprintf(cError,"__PackegCtrlCmdHandle():信息包id=%d的%d条控制命令转换为%d条通用消息成功",
			    Packeg.nPackID,Packeg.ProCmdList.size(),NxCmdList.size());
		RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		//Add by songliang begin at 2016-05-04: 记录控制命令的结果日志.
		__RecordCtrlLogToDb(&NxCmdList);
		//Add by songliang end.
	}
	else
	{
		//Add by songliang begin at 2016-05-04: 记录控制命令解析失败的结果日志.
		__RecordCtrlCmdExpErrToDb(&Packeg.ProCmdList);
		//Add by songliang end.
		sprintf(cError,"__PackegCtrlCmdHandle():信息包id=%d的%d条控制命令转换为通用消息失败,CommonMsg个数:%d,失败回应个数:%d",
			    Packeg.nPackID,Packeg.ProCmdList.size(),NxCmdList.size(),ProResultList.size());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		// 转换失败生成的回应结果直接发送到传输库
		__PutProDataToTransObj(ProResultList,true,true);
		_clear_commonmsg_list(NxCmdList);
		_clear_probody_list(ProResultList);
		return false;
	}
	
	// 修改NX命令invokeid并保存到信息包映射表中
	if( !__SetPackegNxCmdMap(Packeg.nPackID,NxCmdList,Packeg.NxCmdMap) )
	{
		sprintf(cError,"__PackegCtrlCmdHandle():为信息包ID=%d的控制命令设置NX命令映射表失败,该命令不予处理",Packeg.nPackID);
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		_clear_commonmsg_list(NxCmdList);
		_clear_probody_list(ProResultList);
		return false;
	}
	
	// 将NX命令发送给消息业务库
	__PutNxMsgToOperaObj(NxCmdList);
	_clear_commonmsg_list(NxCmdList);
	_clear_probody_list(ProResultList);
	return true;
}

/**
* @brief         命令/结果匹配队列中信息包命令对应的结果收全后进行处理
* @param[in]     ASK_RES_MATCH_PACKAGE & Packeg:命令信息包
* @param[in]     INXEcProConvertObj * pProCvtObj;规约转换对象指针
* @return        bool :true-成功 false-失败
*/
bool  CNXEcSrvProOperation::__PackegResultHandle(IN ASK_RES_MATCH_PACKAGE & Packeg,IN IN INXEcProConvertObj * pProCvtObj)
{
	char cError[510] = "";
	bool bRecvAllResult = true;
	if( pProCvtObj == NULL )
		return false;

	// 确认NX命令映射表中是否所有命令都收到结果
	INVOKEID_TO_NXCMD_MAP::iterator iteNxMap = Packeg.NxCmdMap.begin();
	NX_CMD_INFO * pCmd = NULL;
	while( iteNxMap != Packeg.NxCmdMap.end() )
	{
		pCmd = iteNxMap->second;
		if( pCmd != NULL )
		{
			if( !pCmd->bRecvRes )     // 是否收到结果
			{
				bRecvAllResult = false;
				break;
			}
		}
		iteNxMap ++;
	} 

	// 结果未收全不转换
	if( !bRecvAllResult ) 
		return false;

	// 开始转换
	PRO_FRAME_BODY_LIST ProResultList;
	int nResSize = Packeg.NxResList.size();
	NX_COMMON_MSG_LIST::iterator iteNxRes=Packeg.NxResList.begin();
	while(iteNxRes != Packeg.NxResList.end() )
	{
		if( --nResSize == 0 )  // 最后一条结果置结束标识
			iteNxRes->b_lastmsg = true;
		else
			iteNxRes->b_lastmsg = false;

		pProCvtObj->ConvertCommonMsgToPro(&(*iteNxRes),Packeg.ProCmdList,ProResultList);
		
		sprintf(cError,"__PackegResultHandle():信息包id=%d的命令收全NX结果并转换为%d条规约结果(NX结果:%s)",
			    Packeg.nPackID,ProResultList.size(),_get_commonmsg_desc(*iteNxRes).c_str());
		RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		//Add by songliang begin at 2016-05-04: 记录控制命令的结果日志.
		if (Packeg.bCtrlCmd)
		{
			NX_COMMON_MSG_LIST tmpNxMsgList;
			tmpNxMsgList.push_back(*iteNxRes);
			__RecordCtrlLogToDb(&tmpNxMsgList);
			tmpNxMsgList.clear();
		}
		//Add by songliang end.
		// 将结果发送给传输库
		__PutProDataToTransObj(ProResultList,true,true);
		_clear_probody_list(ProResultList);
		++iteNxRes;
	} 

	// 如果为控制预校命令，且未收到控制执行命令前,该信息包暂不删除,需要等待收到控制执行命令后再删除
	if( (Packeg.bCtrlCmd) && (!Packeg.bRcvLastCtrlExecCmd) )
	{
		if( Packeg.NxResList.size() > 0 )
		{
			// 第一次，清除NX结果链表,避免下次循环重复转换
			_clear_commonmsg_list(Packeg.NxResList);
			// 更新该信息包的时间(再最多等待控制预校超时设置时间)
			Packeg.nMakeTime = time(NULL);
		}
		return false;
	}
	return true;
}

/**
* @brief         命令/结果匹配队列中信息包命令的超时处理
* @param[in]     ASK_RES_MATCH_PACKAGE & Packeg:命令信息包
* @return        bool :true-成功 false-失败
*/
bool CNXEcSrvProOperation::__PackegTimeOutHandle(IN ASK_RES_MATCH_PACKAGE & Packeg)
{
	char cError[510] = "";
	NX_COMMON_MESSAGE FailedMsg;
	// 获得当前时间
	time_t tNow = time(NULL);
	int nFunTimeOut = 0;
	bool bRcvAllRes = true;  // 标识是否收到全部结果

	// NX命令映射表中未收到结果命令的超时判断
	INVOKEID_TO_NXCMD_MAP::iterator iteNxMap = Packeg.NxCmdMap.begin();
	NX_CMD_INFO * pCmd = NULL;
	while( iteNxMap != Packeg.NxCmdMap.end() )
	{
		_init_common_msg_struct(FailedMsg);
		pCmd = iteNxMap->second;
		if( pCmd == NULL )
		{
			iteNxMap ++;
			continue;
		}
		nFunTimeOut = m_pModelSeekIns->GetFunTimeOut(pCmd->NxCmd.n_msg_type);
		if( nFunTimeOut <= 0 )
		{
			nFunTimeOut = 60;   // 默认超时60秒
		}

		if( !pCmd->bRecvRes )     // 未收到结果,自动增加超时失败回应
		{
			bRcvAllRes = false;
			// 判断超时
			if( (tNow - Packeg.nMakeTime) >= nFunTimeOut )
			{
				// 生成失败回应加入信息包结果队列，同时将该命令接收结果标识置为ture
				_make_commonmsg_failed_response(pCmd->NxCmd,FailedMsg);
				Packeg.NxResList.push_back(FailedMsg);
				pCmd->bRecvRes = true;
				sprintf(cError,"__PackegTimeOutHandle():信息包ID=%d中命令:%s 在%d秒内未收到结果,超时失败处理",
					    Packeg.nPackID,_get_commonmsg_desc(pCmd->NxCmd).c_str(),nFunTimeOut);
				RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
				// 如果为控制命令，则将收到最后一帧执行命令标识也置位,已经失败，无需再等待后续执行命令
				if( Packeg.bCtrlCmd )
					Packeg.bRcvLastCtrlExecCmd = true;
			}
		}

		iteNxMap ++;
	} 

	// 如果收到全部结果,但为控制预校命令等待控制执行命令的情况
	if( ( bRcvAllRes) && (Packeg.NxCmdMap.size() > 0) && (Packeg.bCtrlCmd) && (!Packeg.bRcvLastCtrlExecCmd))
	{
		char cDes[255]="";
		if( pCmd != NULL )
		{
			sprintf(cDes,"%s",_get_commonmsg_desc(pCmd->NxCmd).c_str());
		}
		if( (tNow - Packeg.nMakeTime) >= nFunTimeOut )
		{
			Packeg.bRcvLastCtrlExecCmd = true;

			sprintf(cError,"__PackegTimeOutHandle():信息包ID=%d中预校命令:%s 在%d秒内未收到其控制执行命令,不再等待",
				    Packeg.nPackID,cDes,nFunTimeOut);
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		}
	}

	return true;
}


/**
* @brief         事件通知处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__DoEventNotifyLoop()
{
	NX_EVENT_MESSAGE EventMsg;
	PRO_FRAME_BODY_LIST ProList;

	INXEcProConvertObj *pProCvtObj = __CreateOneCvtObj();
	if( pProCvtObj == NULL )
	{
		RcdErrLogWithParentClass("__DoEventNotifyLoop():创建转换对象失败,退出事件处理线程","CNXEcSrvProOperation");
		return -1;
	}

	while ( !m_bEnd )
	{
		if( m_NxEventDeque.empty() || (m_nCurLinkStatus == COMMU_STATUS_OUTLINE) )
		{
			sy_sleep(50);
			continue;
		}

		EventMsg = m_NxEventDeque.front();

		// 转换
		pProCvtObj->ConvertEventMsgToPro(&EventMsg,ProList);

		//通信状态变位发生抖动时，上送需要根据顺序发送，所以新收到的事件交给传输库时放到队列后面 start update by yys 2022年4月11日19:52:00
		//2023年9月24日09:11:33 开普测试 运行状态也有同样情况  update by yys
		//2023年12月20日09:35:20 经讨论，原机制防止在召唤大文件过程中有动作告警或变位事件上送需要排队，想优先上送，但
		//若上送的事件间隔较短，同时处理队列数量>1的情况 会导致信号上送顺序改变，因此都按正常顺序上送。
// 		if ((NX_IED_EVENT_COMMU_REPORT == EventMsg.n_msg_type)||(NX_SYS_EVENT_IED_RUNSTATUS_REPORT == EventMsg.n_msg_type))
// 		{
// 			// 交给规约传输库
// 			__PutProDataToTransObj(ProList,true,true);
// 		}
// 		else
// 		{
// 			// 交给规约传输库
// 			__PutProDataToTransObj(ProList,false,true);
// 		}
		//模拟修改方案 end
		// 交给规约传输库
		__PutProDataToTransObj(ProList,true,true);
		

		EventMsg.list_subfields.clear();
		//_clear_probody_list(ProList);

		// 删除元素
		m_NxEventDeque.pop_front();
	}

	delete pProCvtObj;
	RcdTrcLogWithParentClass("__DoEventNotifyLoop()正常退出","CNXEcSrvProOperation");
	return 0;
}

/**
* @brief         召唤命令处理循环
* @param[in]     无
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__DoCallAskLoop()
{
	char cError[510] ="";
	PRO_FRAME_BODY  ProCmd;

	INXEcProConvertObj *pProCvtObj = __CreateOneCvtObj();
	if( pProCvtObj == NULL )
	{
		RcdErrLogWithParentClass("__DoCallAskLoop():创建转换对象失败,退出规约命令处理线程","CNXEcSrvProOperation");
		return -1;
	}

	while ( !m_bEnd )
	{
		if( m_ProCmdDeque.empty() )
		{
			sy_sleep(50);
			continue;
		}

		ProCmd = m_ProCmdDeque.front();

		// 命令处理
		__ProCmdHandle(ProCmd,pProCvtObj);

		ProCmd.vVarData.clear();
		// 删除元素
		m_ProCmdDeque.pop_front();
	}

	delete pProCvtObj;
	RcdTrcLogWithParentClass("__DoCallAskLoop()正常退出","CNXEcSrvProOperation");

	return 0;
}

/**
* @brief         处理规约命令信息
* @param[in]     PRO_FRAME_BODY& ProCmd:规约命令
* @param[in]     INXEcProConvertObj * pProCvtObj:转换对象
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__ProCmdHandle(IN PRO_FRAME_BODY& ProCmd,IN INXEcProConvertObj * pProCvtObj)
{
	char cError[510] ="";
	EC_PRO_CVT_TYPE eCvtType = CVT_UNKNOWN;
	PRO_FRAME_BODY_LIST ProCmdList,ProResultList;
	NX_COMMON_MSG_LIST  CommonMsgList;

	if( m_nCurLinkStatus == COMMU_STATUS_OUTLINE )  // 已经断开
	{
		sprintf(cError,"__ProCmdHandle():与对端通信断开,规约命令:%s丢弃不予处理",
			    _get_probody_keyDesc(&ProCmd).c_str());
		RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		return 0;
	}

	// 获得转换类型
	eCvtType = pProCvtObj->GetCvtTypeByProInf(&ProCmd);
	if( eCvtType == CVT_TO_CALL )          // 从装置召唤命令
	{
		ProCmdList.push_back(ProCmd);
		// 转换
		if( pProCvtObj->ConvertProToCommonMsg(&ProCmdList,CommonMsgList,ProResultList) >= 0  )
		{
			sprintf(cError,"__ProCmdHandle():规约命令:%s 转换为%d条通用消息成功",
				    _get_probody_keyDesc(&ProCmd).c_str(),CommonMsgList.size());
			RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		}
		else
		{
			sprintf(cError,"__ProCmdHandle():规约命令:%s 转换为通用消息失败,CommonMsg个数:%d,失败回应个数:%d",
				    _get_probody_keyDesc(&ProCmd).c_str(),CommonMsgList.size(),ProResultList.size());
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		}

		// 转换失败生成的回应结果直接发送到传输库
		__PutProDataToTransObj(ProResultList,true,true);

		// 转换生成的通用消息命令增加到命令/结果匹配队列并修改NX命令消息invoke_id后发送给业务库
		if( __AddProCallCmdToAskResDeque(ProCmd,CommonMsgList))
		{
			// 将命令交给业务库
			__PutNxMsgToOperaObj(CommonMsgList);
		}
	}
	else if( eCvtType == CVT_TO_CTRL )     // 控制类命令
	{
		// 控制命令可能多帧,先加入队列，等待后续帧收完后再转换(由命令结果匹配线程完成)
		__AddProCtrlCmdToAskResDeque(ProCmd,pProCvtObj);
	}
	else if( eCvtType == CVT_FROM_LOCAL )  // 直接从本地获取信息
	{
		__GetInfoFromLocal(ProCmd,pProCvtObj);
	}
	else
	{
		sprintf(cError,"__ProCmdHandle()：服务端不支持帧内容为:%s信息的处理",
			    _get_probody_keyDesc(&ProCmd).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
	}

	_clear_commonmsg_list(CommonMsgList);
	_clear_probody_list(ProCmdList);
	_clear_probody_list(ProResultList);

	return 0;
}

/**
* @brief         处理从本地召唤信息的命令
* @param[in]     PRO_FRAME_BODY& ProBody:规约命令
* @param[in]     INXEcProConvertObj * pCvtObj:转换对象
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__GetInfoFromLocal(IN PRO_FRAME_BODY& ProCmd,IN INXEcProConvertObj * pCvtObj)
{
	char cError[510] = "";

	// 获取线程处理
	CALL_CMD_HANDLE_PARAM * pParam = _GetOneCallThread();
	if( pParam == NULL )
	{
		sprintf(cError,"__GetInfoFromLocal():线程池用尽，本次命令(%s)暂无法处理",_get_probody_keyDesc(&ProCmd).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		return -1;
	}

	// 线程参数保存命令
	pParam->ProCmd = ProCmd;

	// 唤醒线程
	if( sy_unname_sem_post(&pParam->hSem) == 0 )
	{
		sprintf(cError,"__GetInfoFromLocal():为命令(%s)分配处理线程成功",_get_probody_keyDesc(&ProCmd).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
	}
	else
	{
		sprintf(cError,"__GetInfoFromLocal():唤醒线程失败，无法处理命令(%s)",_get_probody_keyDesc(&ProCmd).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		return -1;
	}

	return 0;
}

/**
* @brief         规约报文发送结果通知(由传输对象回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     PRO_FRAME_BODY& FrameBody:帧体内容
* @param[in]      bool bResult:发送结果 true-发送成功 false-发送失败
* @return        int 0-成功 其它-失败
*/
PFUNC_ON_PRO_DATA_SEND_RESULT CNXEcSrvProOperation::_OnProFrameSendResultRecv(IN LPVOID pObj, IN PRO_FRAME_BODY& FrameBody, IN bool bResult)
{
	char cError[510] = "";
	if( pObj == NULL )
	{
		printf("CNXEcSrvProOperation::_OnProFrameSendResultRecv()从规约传输对象收发送结果时,回调对象为NULL,无法接收\n");
		return (PFUNC_ON_PRO_DATA_SEND_RESULT)-1;
	}

	CNXEcSrvProOperation* pThis = (CNXEcSrvProOperation*)pObj;

	char cTemp[10] ="";
	if( bResult )
	{
		sprintf(cTemp,"%s","成功");
		sprintf(cError,"_OnProFrameSendResultRecv():传输对象发送帧数据%s.(帧内容:%s)",
			    cTemp,_get_probody_keyDesc(&FrameBody).c_str());
		pThis->RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
	}
	else
	{
		sprintf(cTemp,"%s","失败");
		sprintf(cError,"_OnProFrameSendResultRecv():传输对象发送帧数据%s.(帧内容:%s)",
			    cTemp,_get_probody_keyDesc(&FrameBody).c_str());
		pThis->RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
	}

	return 0;
}

/**
* @brief         接收规约报文(由传输对象回调)
* @param[in]     LPVOID pRegObj：注册对象
* @param[in]     PRO_FRAME_BODY & FrameBody:规约帧数据
* @return        int 0-成功 其它-失败
*/
PFUNC_ON_RECV_PRO_DATA CNXEcSrvProOperation::_OnRecvProFrameData(IN LPVOID pObj, IN PRO_FRAME_BODY & FrameBody)
{
	if( pObj == NULL )
	{
		printf("CNXEcSrvProOperation::_OnRecvProFrameData()从规约传输对象收信息时,回调对象为NULL,无法接收\n");
		return (PFUNC_ON_RECV_PRO_DATA)-1;
	}

	// 添加到规约命令队列
	CNXEcSrvProOperation* pThis = (CNXEcSrvProOperation*)pObj;

	pThis->m_ProCmdDeque.push_back(FrameBody);

	string strLog = "_OnRecvProFrameData()接收到规约命令信息:" + _get_probody_keyDesc(&FrameBody);

	pThis->RcdTrcLogWithParentClass(strLog.c_str(),"CNXEcSrvProOperation");

	return 0;
}

/**
* @brief         接收召唤结果信息（由NX消息业务库回调)
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     NX_COMMON_MESSAGE&: 通用消息内容
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
PFUNC_ON_NXCOMMON_MSG_HANDLE CNXEcSrvProOperation::_OnRecvCommonMsgRes(IN LPVOID pObj,IN NX_COMMON_MESSAGE& Msg)
{
	if( pObj == NULL )
	{
		printf("_OnRecvCommonMsgRes()：回调函数参数中回调对象为NULL,无法处理结果:%s",_get_commonmsg_desc(Msg).c_str());
		return (PFUNC_ON_NXCOMMON_MSG_HANDLE) -1;
	}

	CNXEcSrvProOperation * pProtocol = (CNXEcSrvProOperation *) pObj;
	pProtocol->__AddCallResultToAskResDeque(Msg);

	return 0;
}

/**
* @brief         将通用消息召唤结果添加到命令/结果匹配队列中对应的信息包内
* @param[in]     NX_COMMON_MESSAGE & ResultMsg:结果消息
* @return        bool :true-成功 false-失败
*/
bool CNXEcSrvProOperation::__AddCallResultToAskResDeque(IN NX_COMMON_MESSAGE & ResultMsg)
{
	char cError[510] = "";
	// 锁定队列
	CAutoLockOnStack tmpLock(&m_LockForPackegDeque);

	// 根据结果信息的c_invoke_id(规则:"信息包号/命令列表内命令的索引号")中的信息包号找到信息包
	string strInvoke = ResultMsg.c_invoke_id;
	string::size_type nPosition = strInvoke.find("/");
	if( nPosition == string::npos )  // 没有找到 
	{
		sprintf(cError,"__AddCallResultToAskResDeque():收到结果消息:%s,其c_invoke_id编号规则不合法,丢弃",
			    _get_commonmsg_desc(ResultMsg).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		return false;
	}
	// 获取信息包id
	string strID = strInvoke.substr(0,nPosition);
	int nPackegID = atoi(strID.c_str());

	// 在请求结果队列中查找该信息包
	ASK_RES_MATCH_PACKAGE tmpPackeg;
	tmpPackeg.nPackID = nPackegID;
	ASK_RES_MATCH_PAK_DEQUE::iterator itePackeg; 
	itePackeg = find(m_AskResPackegDeque.begin(),m_AskResPackegDeque.end(),tmpPackeg);
	if( itePackeg == m_AskResPackegDeque.end() )
	{
		sprintf(cError,"__AddCallResultToAskResDeque():没有命令包与结果消息(%s) 对应,丢弃",
			    _get_commonmsg_desc(ResultMsg).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		return false;
	}

	// 加入结果队列
	itePackeg->NxResList.push_back(ResultMsg);

	// 设置命令的结果回应情况(根据c_invoke_id找到对应的命令)
	INVOKEID_TO_NXCMD_MAP::iterator iteNxMap;
	iteNxMap = itePackeg->NxCmdMap.find(strInvoke);
	if( iteNxMap == itePackeg->NxCmdMap.end() )
	{
		sprintf(cError,"__AddCallResultToAskResDeque():命令包(PackegID=%d)的命令队列中没有命令与结果消息(%s) 对应",
			    nPackegID,_get_commonmsg_desc(ResultMsg).c_str());
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		return false;
	}

	// 设置对应命令的接收结果情况
	NX_CMD_INFO * pCmd = iteNxMap->second;
	if( pCmd != NULL )
		pCmd->bRecvRes = true;

	sprintf(cError,"__AddCallResultToAskResDeque():增加结果消息(%s) 到命令包(PackegID=%d)中成功",
		    _get_commonmsg_desc(ResultMsg).c_str(),nPackegID);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
	return true;
}

/**
* @brief         接收事件信息（由NX消息业务库回调)
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     NX_EVENT_MESSAGE&: 事件消息内容
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
PFUNC_ON_NXEVENT_MSG_HANDLE  CNXEcSrvProOperation::_OnRecvEventMsg(IN LPVOID pObj,IN NX_EVENT_MESSAGE& Msg)
{
	char cError[510]="";
	if( pObj == NULL )
	{
		printf("CNXEcSrvProOperation::__OnRecvEventMsg()从NX消息业务库收事件信息时，回调对象为NULL,无法接收\n");
		return (PFUNC_ON_NXEVENT_MSG_HANDLE)-1;
	}

	// 添加到事件队列
	CNXEcSrvProOperation* pThis = (CNXEcSrvProOperation*)pObj;

	while( pThis->m_NxEventDeque.size() >= MAX_EVENT_DEQUE_LEN )
	{
		pThis->m_NxEventDeque.pop_front();
		sprintf(cError,"_OnRecvEventMsg():事件队列中元素个数=%d,超过最大长度:%d,删除头元素",
			    pThis->m_NxEventDeque.size(),MAX_EVENT_DEQUE_LEN);
		pThis->RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
	}

	// 	if( Msg.n_msg_type == NX_IED_EVENT_EVENT_REPORT)  //动作优先处理
	// 		pThis->m_NxEventDeque.push_front(Msg);
	// 	else
	pThis->m_NxEventDeque.push_back(Msg);
	return 0;
}

/**
* @brief         清空请求结果信息包内容
* @param[in]     ASK_RES_MATCH_PACKAGE & Pack:信息包
* @param[in]     无
* @return        void
*/
void CNXEcSrvProOperation::_ClearAskResPackeg(IN ASK_RES_MATCH_PACKAGE & Pack)
{
	// 规约命令
	PRO_FRAME_BODY tmpBody;
	while (Pack.ProCmdList.size() > 0 )
	{
		tmpBody = Pack.ProCmdList.front();
		tmpBody.vVarData.clear();
		Pack.ProCmdList.pop_front();
	}
	Pack.ProCmdList.clear();

	// NX命令映射表
	INVOKEID_TO_NXCMD_MAP::iterator ite = Pack.NxCmdMap.begin();
	NX_CMD_INFO * pCmdInfo = NULL;
	while (ite != Pack.NxCmdMap.end())
	{
		pCmdInfo = ite->second;
		if( pCmdInfo != NULL )
		{
			pCmdInfo->NxCmd.list_subfields.clear();
			delete pCmdInfo;
			pCmdInfo = NULL;
		}
		++ite;
	}
	Pack.NxCmdMap.clear();

	// 通用消息结果列表
	NX_COMMON_MESSAGE Msg;
	while (Pack.NxResList.size() > 0 )
	{
		Msg = Pack.NxResList.front();
		Msg.list_subfields.clear();
		Pack.NxResList.pop_front();
	}
	Pack.NxResList.clear();

	Pack.nPackID = 0;
	Pack.nMakeTime = 0;
}

/**
* @brief         将通用消息命令增加到命令/结果匹配队列并发送给业务库
* @param[in]     PRO_FRAME_BODY& ProCmd:规约命令
* @param[in]     NX_COMMON_MSG_LIST& MsgList:通用消息链表
* @return        bool :true-成功 false-失败
*/
bool CNXEcSrvProOperation::__AddProCallCmdToAskResDeque(IN PRO_FRAME_BODY& ProCmd,IN NX_COMMON_MSG_LIST& MsgList)
{
	char cError[510] = "";
	NX_CMD_INFO * pCmd = NULL;
	if( MsgList.size() <= 0 )
		return 0;
	NX_COMMON_MSG_LIST::iterator iteCommonMsg = MsgList.begin();
	// 锁定队列
	CAutoLockOnStack tmpLock(&m_LockForPackegDeque);
	ASK_RES_MATCH_PAK_DEQUE::iterator itePackeg = m_AskResPackegDeque.begin();
	INVOKEID_TO_NXCMD_MAP::iterator iteNxMap;
	// 根据转换完毕的NX消息命令在已有信息包队列中查找，需要合并的进行命令合并，无需合并的进行缓存
	// NX消息命令只需一条与已有的完全一致，即可判断为一致
	// update by jjl 20140918 ,对于从装置召唤的信息，不再限制命令类型,为提高效率,都支持合并处理
	/*if( (iteCommonMsg->n_msg_type == NX_IED_CALL_SG_ASK)        || 
		(iteCommonMsg->n_msg_type == NX_IED_CALL_SGZONE_ASK)    ||
		(iteCommonMsg->n_msg_type == NX_IED_CALL_SOFTSTRAP_ASK) || 
		(iteCommonMsg->n_msg_type == NX_IED_CALL_ANALOG_ASK)    || 
		(iteCommonMsg->n_msg_type == NX_IED_CALL_HARDSTRAP_ASK)
		)
	{ */ 
		while( itePackeg != m_AskResPackegDeque.end() )
		{
			if( itePackeg->bCtrlCmd ) // 控制命令跳过
			{
				++itePackeg;
				continue;
			}
			// 在已有的NX映射表中查找
			iteNxMap = itePackeg->NxCmdMap.begin();
			while( iteNxMap != itePackeg->NxCmdMap.end() )
			{
				pCmd = iteNxMap->second;
				if( pCmd == NULL )
				{
					iteNxMap++;
					continue;
				}
				if( ( iteCommonMsg->n_msg_type == pCmd->NxCmd.n_msg_type)      &&
					( iteCommonMsg->n_obj_id == pCmd->NxCmd.n_obj_id )         && 
					( iteCommonMsg->n_obj_type == pCmd->NxCmd.n_obj_type )     &&
					( iteCommonMsg->n_sub_obj_id == pCmd->NxCmd.n_sub_obj_id ) &&
					( iteCommonMsg->n_sub_sub_obj_id == pCmd->NxCmd.n_sub_sub_obj_id )
					)
				{
					// 将现有命令直接增加到该信息包的命令队列
					itePackeg->ProCmdList.push_back(ProCmd);
					// 清空转换后的通用消息命令队列
					_clear_commonmsg_list(MsgList);
					sprintf(cError,"__AddProCallCmdToAskResDeque():规约命令:%s 转换为NX命令后与已有信息包ID=%d命令一致,不重复下发,进行合并",
						    _get_probody_keyDesc(&ProCmd).c_str(),itePackeg->nPackID);
					RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
					return true;
				}
				iteNxMap ++;
			} // while( iteNxMap != itePackeg->NxCmdMap.end() )
			itePackeg ++;
		} // while( itePackeg != m_AskResPackegDeque.end() )
/*	} */ // if(命令类型判断)

	// 没有找到合并命令或无需合并时增加一个信息包
	ASK_RES_MATCH_PACKAGE newPackeg;
	newPackeg.nPackID = m_nPackegId;
	newPackeg.bCtrlCmd = false;
	newPackeg.bRcvLastCmd = ProCmd.bLast;
	newPackeg.nMakeTime = time(NULL);
	newPackeg.ProCmdList.push_back(ProCmd);

	// 修改NX列表中命令InVokeID,并设置信息包中INVOKEID与NX命令映射表
	if( !__SetPackegNxCmdMap(m_nPackegId,MsgList,newPackeg.NxCmdMap) )
	{
		sprintf(cError,"__AddProCallCmdToAskResDeque():为命令:%s 生成新信息包(ID=%d)时设置NX命令映射表失败",
			    _get_probody_keyDesc(&ProCmd).c_str(),m_nPackegId);
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		_ClearAskResPackeg(newPackeg);
		return false;
	}

	m_AskResPackegDeque.push_back(newPackeg);

	sprintf(cError,"__AddProCallCmdToAskResDeque():为规约命令:%s 生成一个新信息包(编号=%d)",
		    _get_probody_keyDesc(&ProCmd).c_str(),m_nPackegId);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");

	// 信息包编号加1
	m_nPackegId ++;

	return true;
}

/**
* @brief         修改NX列表中命令InVokeID,并设置信息包中INVOKEID与NX命令映射表
* @param[in]     int nPackegID:信息包编号
* @param[in]     NX_COMMON_MSG_LIST& MsgList:NX命令列表
* @param[out]    INVOKEID_TO_NXCMD_MAP &NxCmdMap:关系映射表
* @return        bool: true-成功 false：失败
*/
bool CNXEcSrvProOperation::__SetPackegNxCmdMap(IN int nPackegID,IN NX_COMMON_MSG_LIST& MsgList,OUT INVOKEID_TO_NXCMD_MAP &NxCmdMap)
{
	char cError[510]="";
	int nIndex =0;
	NX_CMD_INFO * pCmdInfo = NULL;
	NX_COMMON_MSG_LIST::iterator iteCommonMsg = MsgList.begin();
	while( iteCommonMsg != MsgList.end() )
	{
		// 设置nx 通用消息命令的invokedid(规则:"信息包号/该列表内命令的索引号")
		_ZERO_MEM(iteCommonMsg->c_invoke_id,sizeof(iteCommonMsg->c_invoke_id));
		sprintf( iteCommonMsg->c_invoke_id,"%d/%d",nPackegID,nIndex);
		pCmdInfo = new NX_CMD_INFO;
		if( pCmdInfo != NULL )
		{
			pCmdInfo->NxCmd = (*iteCommonMsg);
			pCmdInfo->bRecvRes = false;
			NxCmdMap[iteCommonMsg->c_invoke_id] = pCmdInfo;
		}
		else
		{
			sprintf(cError,"__SetPackegNxCmdMap():为PakcegID=%d的新信息包设置NX命令映射表时分配内存失败",nPackegID);
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
			return false;
		}
		iteCommonMsg ++;
		nIndex++;
		pCmdInfo = NULL;
	}

	return true;
}

/**
* @brief         将规约控制命令增加到命令/结果匹配队列
* @param[in]     PRO_FRAME_BODY& ProCmd:规约命令
* @param[in]     INXEcProConvertObj * pCvtObj;规约转换对象指针
* @return        bool :true-成功 false-失败
*/
bool CNXEcSrvProOperation::__AddProCtrlCmdToAskResDeque(IN PRO_FRAME_BODY& ProCmd,IN INXEcProConvertObj * pCvtObj)
{
	char cError[510] = "";
	if( pCvtObj == NULL )
		return false;
	bool bCtrlExecCmd = false;  // 标识本帧是否为控制执行帧

	// 锁定队列
	CAutoLockOnStack tmpLock(&m_LockForPackegDeque);
	ASK_RES_MATCH_PAK_DEQUE::iterator itePackeg = m_AskResPackegDeque.begin();
	PRO_FRAME_BODY_LIST::iterator iteProCmd;
	// 根据规约命令在已有命令信息包队列中查找，如果是多帧命令的后续帧进行合并，否则进行缓存
	while( itePackeg != m_AskResPackegDeque.end() )
	{
		if(  !itePackeg->bCtrlCmd  ) // 非控制命令跳过
		{
			++itePackeg;
			continue;
		}
		// 在已有的规约命令链表中查找
		iteProCmd = itePackeg->ProCmdList.begin();
		while( iteProCmd != itePackeg->ProCmdList.end() )
		{
			if( pCvtObj->IsFollowUpFrame((*iteProCmd),ProCmd) ) // 判断是否为同一命令的后续帧
			{
				// 将现有命令直接增加到该信息包的命令队列
				itePackeg->ProCmdList.push_back(ProCmd);
				itePackeg->bRcvLastCmd = ProCmd.bLast;

				sprintf(cError,"__AddProCtrlCmdToAskResDeque():控制命令:%s 与已有信息包ID=%d的命令一致,进行合并",
					    _get_probody_keyDesc(&ProCmd).c_str(),itePackeg->nPackID);
				RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
				return true;
			}

			// 如果两个命令分别为同一次控制命令的预校和执行命令(需通过预校命令更新执行命令)
			if( pCvtObj->UpdateDisFrameBySrcFrame((*iteProCmd),ProCmd) )
			{
				 // 修改预校命令信息包中是否收到最后一帧执行命令标识
				itePackeg->bRcvLastCtrlExecCmd = ProCmd.bLast;
				bCtrlExecCmd = true;
				sprintf(cError,"__AddProCtrlCmdToAskResDeque():控制执行命令:%s 与已有信息包ID=%d的预校命令匹配,更新控制命令信息成功",
					    _get_probody_keyDesc(&ProCmd).c_str(),itePackeg->nPackID);
				RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
				break;
			}
			iteProCmd ++;
		} // while( iteProCmd != itePackeg->ProCmdList.end() )
		itePackeg ++;

		if( bCtrlExecCmd )  // 如果已经为控制命令,无需再向下遍历
			break;
	} // while( itePackeg != m_AskResPackegDeque.end() )

	// 不需要合并命令时增加一个信息包
	ASK_RES_MATCH_PACKAGE newPackeg;
	newPackeg.nPackID = m_nPackegId;
	newPackeg.bCtrlCmd = true;
	newPackeg.bRcvLastCmd = ProCmd.bLast;
	if( bCtrlExecCmd )  // 如果本身为控制执行命令,则需更新对应的收到最后一帧控制执行命令标识
		newPackeg.bRcvLastCtrlExecCmd = ProCmd.bLast;

	newPackeg.nMakeTime = time(NULL);
	newPackeg.ProCmdList.push_back(ProCmd);

	m_AskResPackegDeque.push_back(newPackeg);

	sprintf(cError,"__AddProCtrlCmdToAskResDeque():为控制命令:%s生成一个新信息包(编号=%d),bRcvLastCtrlExecCmd=%d",
		    _get_probody_keyDesc(&ProCmd).c_str(),m_nPackegId,newPackeg.bRcvLastCtrlExecCmd);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");

	// 信息包编号加1
	m_nPackegId ++;
	_clear_probody_list(newPackeg.ProCmdList);
	return true;
}

/**
* @brief         启动指定个数的召唤命令处理线程并加入线程管理队列
* @param[in]     int nThreadNum:指定的个数
* @param[in]     bool bStart: true-启动并加入管理队列 false-仅加入管理队列,不启动线程
* @param[out]    无
* @return        bool true-成功 false-失败
*/
bool CNXEcSrvProOperation::_StartProCmdThreadPool(IN int nThreadNum,IN bool bStart) 
{
	char cError[510]="";
	char cDes[100]="";
	EC_THREAD_INFO * pThreadInfo = NULL;
	int i=0,nStartNum = 0;

	// 个数合法性判断
	if( nThreadNum >= THREAD_POOL_MAX_CAPACITY )
		return false;

	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart )     // 已经启动
			continue;

		if( sy_unname_sem_init(&m_CallPoolParam[i].hSem,0) != 0 )
		{
			sprintf(cError,"_StartProCmdThreadPool():初始化inxdex=%d召唤命令线程参数的无名信号量失败,原因:%s(%d)",
				    i,strerror(errno),errno);
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
			return false;
		}

		//启动线程
		pThreadInfo = new EC_THREAD_INFO;
		if( pThreadInfo == NULL)
		{
			sprintf(cError,"为index=%d的召唤命令线程分配线程信息内存失败,无法初始化",i);
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
			sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
			return false;
		}

		pThreadInfo->pSelfObj         = this;
		_ZERO_MEM(cDes,100);
		sprintf(cDes,"index=%d的召唤命令处理线程__CallCmdLoop()",i);
		pThreadInfo->strThreadDes     =cDes;
		pThreadInfo->pCallBackFunc    =_OnCallCmdThreadExec;
		pThreadInfo->pParam           = &m_CallPoolParam[i];
		if( bStart ) 
		{
			if( !StartOneThread(pThreadInfo) )
			{
				delete pThreadInfo;
				sy_unname_sem_destroy(&m_CallPoolParam[i].hSem);
				return false;
			}
		}
		// 加入线程管理队列
		AddThreadInfo(pThreadInfo);
		pThreadInfo = NULL;

		// 修改启动标识
		m_CallPoolParam[i].bThreadStart = true;
		nStartNum++;
		if( nStartNum == nThreadNum )
			break;
	}

	if( nStartNum != nThreadNum )
	{
		sprintf(cError,"_StartProCmdThreadPool():启动%d个线程,小于要求个数(%d),可能线程池用完",nStartNum,nThreadNum);
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		if(nStartNum <= 0 )
			return false;
	}
	return true;
}

/**
* @brief         获取一个空闲的召唤命令处理线程
* @param[in]     无
* @param[out]    无
* @return        CALL_CMD_HANDLE_PARAM:召唤线程参数结构
*/
CALL_CMD_HANDLE_PARAM * CNXEcSrvProOperation::_GetOneCallThread()
{
	char cError[510]="";
	int i = 0;
	for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
	{
		if( m_CallPoolParam[i].bThreadStart && m_CallPoolParam[i].bFree )   // 已经启动其空闲
		{
			m_CallPoolParam[i].bFree = false;
			return &m_CallPoolParam[i];
		}
	}

	// 没有找到时启动1个
	if( _StartProCmdThreadPool(1) )
	{
		for( i =0; i<THREAD_POOL_MAX_CAPACITY; i++ )
		{
			if( m_CallPoolParam[i].bThreadStart && m_CallPoolParam[i].bFree )   // 已经启动且为空闲
			{
				m_CallPoolParam[i].bFree = false;
				return &m_CallPoolParam[i];
			}
		}
	}

	RcdErrLogWithParentClass("_GetOneCallThread()获取线程失败，可能线程池用尽","CNXEcSrvProOperation");
	return NULL;
}

/**
* @brief         召唤命令处理线程池回调执行函数
* @param[in]     LPVOID pObj: 回调对象
* @param[in]     LPVOID pParam: 参数
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::_OnCallCmdThreadExec(LPVOID pObj,LPVOID pParam)
{
	char cError[510]    = "";

	if( ( NULL == pObj ) || (pParam == NULL ) )
		return THREAD_RET_VALUE;
	CALL_CMD_HANDLE_PARAM * pCmd = (CALL_CMD_HANDLE_PARAM *)pParam;

	CNXEcSrvProOperation * pProtocol = (CNXEcSrvProOperation *)pObj;

	try
	{
		pProtocol->__CallCmdLoop(pCmd);
	}
	catch(...)
	{
		sprintf(cError,"召唤命令处理线程池中index=%d的线程异常退出,原因:%s:(%d)",
			    pCmd->nIndex,strerror(errno),errno);
		pProtocol->RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
	}

	return THREAD_RET_VALUE;
}

/**
* @brief         召唤命令线程池中线程的处理主循环
* @param[in]     CALL_CMD_HANDLE_PARAM * pParam:召唤命令线程参数
* @param[in]     无
* @return        int: 0-成功 其它：错误码
*/
int CNXEcSrvProOperation::__CallCmdLoop(IN CALL_CMD_HANDLE_PARAM * pParam)
{
	char cError[510]="";
	EC_PRO_CVT_TYPE eCvtType = CVT_UNKNOWN;
	PRO_FRAME_BODY_LIST ProResultList;

	if( pParam == NULL )
		return -1;
	
	CALL_CMD_HANDLE_PARAM * pCmdPram = (CALL_CMD_HANDLE_PARAM*)pParam;

	INXEcProConvertObj * pProCvtObj = __CreateOneCvtObj();
	if( pProCvtObj == NULL )
	{
		sprintf(cError,"__CallCmdLoop()：index=%d的召唤命令处理线程启动时创建转换对象失败,退出运行",pCmdPram->nIndex);
		RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		pCmdPram->ProCmd.vVarData.clear();
		pCmdPram->bFree = true;
		pCmdPram->bThreadStart = false;
		sy_unname_sem_destroy(&pCmdPram->hSem);
		return 0;
	}

	// 退出标识置为false
	pCmdPram->bExit = false;
	sprintf(cError,"__CallCmdLoop():召唤命令处理线程池中index=%d的线程开始运行,退出标识:%d",pParam->nIndex,pParam->bExit);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");

	while( sy_unname_sem_wait(&pCmdPram->hSem) == 0 )
	{
		_ZERO_MEM(cError,510);
		sprintf(cError,"__CallCmdLoop():召唤命令处理线程池中index=%d的线程被激活",pCmdPram->nIndex);
		RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");

		if( m_bEnd )
		{
			break;
		}
		
		// 获得转换类型
		eCvtType = pProCvtObj->GetCvtTypeByProInf(&pCmdPram->ProCmd);
		if( eCvtType == CVT_FROM_LOCAL )          // 从本地召唤命令
		{
			if( pProCvtObj->DirectResFromLocal(&pCmdPram->ProCmd,ProResultList) == 0 )
			{
				sprintf(cError,"__CallCmdLoop():规约命令:%s 直接从本地获取结果,生成%d条结果报文",
					    _get_probody_keyDesc(&pCmdPram->ProCmd).c_str(),ProResultList.size());
				RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
			}
			else
			{
				sprintf(cError,"__CallCmdLoop():规约命令:%s 直接从本地获取结果失败,生成失败回应%d条",
					    _get_probody_keyDesc(&pCmdPram->ProCmd).c_str(),ProResultList.size());
				RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
			}

			// 回应结果直接发送到传输库
			__PutProDataToTransObj(ProResultList,true,true);
		}
		else
		{
			sprintf(cError,"__CallCmdLoop():不支持处理规约命令:%s",_get_probody_keyDesc(&pCmdPram->ProCmd).c_str());
			RcdErrLogWithParentClass(cError,"CNXEcSrvProOperation");
		}

		pCmdPram->ProCmd.vVarData.clear();
		_clear_probody_list(ProResultList);
		pCmdPram->bFree = true;  // 置为空闲状态

		sprintf(cError,"__CallCmdLoop():召唤命令处理线程池中index=%d的线程完成当前任务,置为空闲",pCmdPram->nIndex);
		RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
		sy_sleep(10);

	} // while( sy_unname_sem_wait(&pCmdPram->hSem) == 0 )
	pCmdPram->ProCmd.vVarData.clear();
	pCmdPram->bFree = true;
	pCmdPram->bExit = true;
	delete pProCvtObj;
	sprintf(cError,"__CallCmdLoop()：index=%d的召唤命令处理线程正常退出,退出标识置位(%d)",pCmdPram->nIndex,pCmdPram->bExit);
	RcdTrcLogWithParentClass(cError,"CNXEcSrvProOperation");
	return 0;
}

/**
* @brief		记录控制日志.			
* @param[in]    NX控制命令队列.
//Add by songliang begin at 2016-05-04
**/
void CNXEcSrvProOperation::__RecordCtrlLogToDb(NX_COMMON_MSG_LIST * pNxCmdList)
{
	char cErr[250] = "";
	NX_COMMON_MSG_LIST::iterator iteCmd = pNxCmdList->begin();
	while( iteCmd != pNxCmdList->end())
	{
		//
		NX_COMMON_MESSAGE TmpNxCmd = *iteCmd;

		CTRL_INFO_RECORD CtrlInfoSet;
		const EC_IED * pEcIedTb = m_pModelSeekIns->GetIedCfgByID(TmpNxCmd.n_obj_id);
		if ( NULL == pEcIedTb )
		{
			sprintf(cErr,"获取IED=%d的配置信息失败.",TmpNxCmd.n_obj_id);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			return;
		}
		const SUBSTATION_TB * pSubStnTB = m_pModelSeekIns->GetSubStationBasicCfg();
		if ( NULL == pSubStnTB )
		{
			RcdErrLogWithParentClass("获取当前子站配置信息失败.","CNXEcSrvProOperation");
			return;
		}

		memcpy(CtrlInfoSet.cRemoteCtrlStnName,m_pProParam->ClientCfg.strCliName.c_str(),__getmin(m_pProParam->ClientCfg.strCliName.length(),127));
		memcpy(CtrlInfoSet.cRemoteCtrlStnIp,m_pProParam->CommuCfg.OppositeNetAddr.TrunkSrvAddr.TrunkNetAddr.cIP,64);
		CtrlInfoSet.tCtrlTime = time(NULL);
		memcpy(CtrlInfoSet.cSelfStnName,pSubStnTB->str_aliasname.c_str(),__getmin(pSubStnTB->str_aliasname.length(),127));
		memcpy(CtrlInfoSet.cPrimDevName,pEcIedTb->pPrimDevCfg->str_aliasname.c_str(),__getmin(pEcIedTb->pPrimDevCfg->str_aliasname.length(),127));
		
		memcpy(CtrlInfoSet.cSecDevName,pEcIedTb->pIed->str_aliasname.c_str(),__getmin(pEcIedTb->pIed->str_aliasname.length(),127));
		CtrlInfoSet.nCpu		= TmpNxCmd.n_sub_obj_id; 
		CtrlInfoSet.nZone		= TmpNxCmd.n_sub_sub_obj_id;
		CtrlInfoSet.nCtrlType	= TmpNxCmd.n_msg_type;
		CtrlInfoSet.bResult		= (TmpNxCmd.n_result==0)?true:false;
		__CvtNxMsgToLogStruct(CtrlInfoSet,TmpNxCmd.list_subfields,pEcIedTb,CtrlInfoSet.nCtrlType);
		__WriteLogToDb(CtrlInfoSet);
		iteCmd++;
	}
}

/**
* @brief		记录控制日志.			
* @param[in]    NX控制命令队列.
//Add by songliang begin at 2016-05-04
**/
void CNXEcSrvProOperation::__RecordCtrlCmdExpErrToDb(PRO_FRAME_BODY_LIST * pProCmdList)
{
	char cErr[250] = "";
	PRO_FRAME_BODY_LIST::iterator iteProCmd = pProCmdList->begin();
	while( iteProCmd != pProCmdList->end())
	{
		//
		PRO_FRAME_BODY ProFrame = *iteProCmd;

		CTRL_INFO_RECORD CtrlInfoSet;

		memcpy(CtrlInfoSet.cRemoteCtrlStnName,m_pProParam->ClientCfg.strCliName.c_str(),__getmin(m_pProParam->ClientCfg.strCliName.length(),127));
		memcpy(CtrlInfoSet.cRemoteCtrlStnIp,m_pProParam->CommuCfg.OppositeNetAddr.TrunkSrvAddr.TrunkNetAddr.cIP,64);
		CtrlInfoSet.tCtrlTime = time(NULL);

		INXEcModelMgr * pINXEcModelMgr = m_pModelSeekIns->GetModelMgrObj();
		if ( NULL == pINXEcModelMgr)
		{
			RcdErrLogWithParentClass("获取INXEcModelMgr指针失败.","CNXEcSrvProOperation");
			return;
		}
		DB_OPER_PARAM DbOperParam;
		DB_FIELD_DATA suField;
		char cDbInsertErr[200] = "";
		char cErr[600]="";

		suField.str_fd_name		= "usr_obj";
		suField.str_fd_value	= CtrlInfoSet.cRemoteCtrlStnName;
		suField.e_fd_type		= FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "compname";
		suField.str_fd_value	= CtrlInfoSet.cRemoteCtrlStnIp;
		suField.e_fd_type		= FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "oprtm";
		char cTmp[512]="";
		struct tm *local = localtime(&CtrlInfoSet.tCtrlTime); 
		strftime(cTmp,100,"%Y-%m-%d %H:%M:%S",local);
		suField.str_fd_value = cTmp;
		suField.e_fd_type		= FD_TYPE_DATETIME;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "function_obj";
		suField.str_fd_value	= "远控";
		suField.e_fd_type		= FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "log_obj";
		suField.str_fd_value	= "控制命令解析失败";
		suField.e_fd_type		= FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "log_desc";
		suField.str_fd_value = "收到的控制命令中,信息有误,无法执行.";
		suField.e_fd_type = FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		suField.str_fd_name		= "oprresult";
		suField.str_fd_value	=  "0";
		suField.e_fd_type		= FD_TYPE_NUMERIC;
		DbOperParam.lst_fddata.push_back(suField);		

		suField.str_fd_name		= "oprflag";
		suField.str_fd_value	=  "100000";
		suField.e_fd_type		= FD_TYPE_NUMERIC;
		DbOperParam.lst_fddata.push_back(suField);

		DbOperParam.lst_tablename.push_back("nx_t_usr_oprlog_ctrl");

		if ( 0 != pINXEcModelMgr->dbm_insert_record(&DbOperParam,cDbInsertErr))
		{
			sprintf(cErr,"控制命令解析错误日志存库失败:%s.",cDbInsertErr);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		}

		iteProCmd++;
	}
}

/**
* @brief		转换nx消息中的数据到日志结构体中.	
//Add by songliang begin at 2016-05-04
**/
void CNXEcSrvProOperation::__CvtNxMsgToLogStruct(CTRL_INFO_RECORD & CtrlInfoSet,vector<NX_COMMON_FIELD_STRUCT> & list_subfields,const EC_IED * pEcIedTb ,int nType)
{
	char cErr[200] = "";
	string strFindKey;
	char cGetNameErr[]="Get name error.";
	char cGetOrgValue[]="Get Value error.";
	for ( int i=0; i<list_subfields.size(); i++ )
	{
		CTRL_INFO OneInfo;
		char TmpValue[128] = "";
		char cKey[50] = "";
		OneInfo.nInfoId = list_subfields[i].n_field_id;
		sprintf(cKey,"%d/%d",CtrlInfoSet.nCpu,OneInfo.nInfoId);
		strFindKey = cKey;
		switch(nType)
		{
		case NX_IED_CTRL_SG_CHECK_ASK:	
		case NX_IED_CTRL_SG_CHECK_REP:
		case NX_IED_CTRL_SG_EXC_ASK:
		case NX_IED_CTRL_SG_EXC_REP:
			{
				EC_SG_MAP::const_iterator iteFindSg = pEcIedTb->sgMap.find(strFindKey);
				if ( iteFindSg != pEcIedTb->sgMap.end() )
				{
					const SG_TB* pSg = iteFindSg->second;
					memcpy(OneInfo.cName,pSg->str_aliasname.c_str(),__getmin(pSg->str_aliasname.length(),127));
				}
				else
				{
					memcpy(OneInfo.cName,cGetNameErr,strlen(cGetNameErr));
				}
				sprintf(TmpValue,"修改为:%s",list_subfields[i].c_value);
				memcpy(OneInfo.cCtrlValue,TmpValue,128);
			}
			break;
		case NX_IED_CTRL_SOFTSTRAP_CHECK_ASK:
		case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
		case NX_IED_CTRL_SOFTSTRAP_CHECK_REP:
		case NX_IED_CTRL_SOFTSTRAP_EXC_REP:
			{
				EC_SOFTSTRAP_MAP::const_iterator iteFindSoft = pEcIedTb->softMap.find(strFindKey);
				if ( iteFindSoft != pEcIedTb->softMap.end() )
				{
					const STRAP_TB * pSoft = iteFindSoft->second;
					memcpy(OneInfo.cName,pSoft->str_aliasname.c_str(),__getmin(pSoft->str_aliasname.length(),127));
				}
				else
				{
					memcpy(OneInfo.cName,cGetNameErr,strlen(cGetNameErr));
				}
				sprintf(TmpValue,"置为:%s",(list_subfields[i].n_value == 1) ? "投":"退");				
				memcpy(OneInfo.cCtrlValue,TmpValue,128);				
			}			
			break;
		case NX_IED_CTRL_SGZONE_CHECK_ASK:
		case NX_IED_CTRL_SGZONE_EXC_ASK:
		case NX_IED_CTRL_SGZONE_CHECK_REP:
		case NX_IED_CTRL_SGZONE_EXC_REP:
			{
				EC_ZONE_MAP::const_iterator iteFindZone = pEcIedTb->zoneMap.find(strFindKey);
				if ( iteFindZone != pEcIedTb->zoneMap.end() )
				{
					const SGZONE_TB * pZone = iteFindZone->second;
					memcpy(OneInfo.cName,pZone->str_aliasname.c_str(),__getmin(pZone->str_aliasname.length(),127));
				}
				else
				{
					memcpy(OneInfo.cName,cGetNameErr,strlen(cGetNameErr));
				}
				sprintf(TmpValue,"切到:%d区",list_subfields[i].n_value);
				memcpy(OneInfo.cCtrlValue,TmpValue,128);
			}
			break;
		default:
			sprintf(cErr,"NXmsg中,消息类型值为%d,不处理.",nType);
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			continue;			
		}
		CtrlInfoSet.vCtrlValue.push_back(OneInfo);
	}
}

/**
* @brief		将格式化好的日志结构写入数据库日志表.			
* @param[in]    CTRL_INFO_RECORD & CtrlInfoSet
* @note //Add by songliang begin at 2016-05-04
**/
void CNXEcSrvProOperation::__WriteLogToDb(CTRL_INFO_RECORD & CtrlInfoSet)
{
	INXEcModelMgr * pINXEcModelMgr = m_pModelSeekIns->GetModelMgrObj();
	if ( NULL == pINXEcModelMgr)
	{
		RcdErrLogWithParentClass("获取INXEcModelMgr指针失败.","CNXEcSrvProOperation");
		return;
	}
	RcdTrcLogWithParentClass("将控制日志写入数据库.","CNXEcSrvProOperation");
	DB_OPER_PARAM DbOperParam;
	DB_FIELD_DATA suField;
	char cDbInsertErr[200] = "";
	char cErr[600]="";

	string strResult = CtrlInfoSet.bResult?"成功":"失败";

	suField.str_fd_name		= "usr_obj";
	suField.str_fd_value	= CtrlInfoSet.cRemoteCtrlStnName;
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "compname";
	suField.str_fd_value	= CtrlInfoSet.cRemoteCtrlStnIp;
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "oprtm";
	char cTmp[512]="";
	struct tm *local = localtime(&CtrlInfoSet.tCtrlTime); 
	strftime(cTmp,100,"%Y-%m-%d %H:%M:%S",local);
	suField.str_fd_value = cTmp;
	suField.e_fd_type		= FD_TYPE_DATETIME;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "function_obj";
	suField.str_fd_value	= "远控";
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "log_obj";
	memset(cTmp,0,512);
	sprintf(cTmp,"变电站[%s]-一次设备[%s]-设备[%s]",CtrlInfoSet.cSelfStnName,CtrlInfoSet.cPrimDevName,CtrlInfoSet.cSecDevName);
	char cTmp0[256]="";
	memcpy(cTmp0,cTmp,255);
	suField.str_fd_value	= cTmp0;
	suField.e_fd_type		= FD_TYPE_CHAR;
	DbOperParam.lst_fddata.push_back(suField);

	string strDesc;
	switch(CtrlInfoSet.nCtrlType)
	{
	case NX_IED_CTRL_SG_CHECK_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.修改定值.预校][详细内容:";
		break;
	case NX_IED_CTRL_SG_CHECK_REP:			
		strDesc  = "[上送控制结果.修改定值.预校.";
		strDesc += strResult + "][详细内容:";
		break;
	case NX_IED_CTRL_SG_EXC_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.修改定值.执行][详细内容:";
		break;
	case NX_IED_CTRL_SG_EXC_REP:			
		strDesc  = "[上送控制结果.修改定值.执行.";
		strDesc += strResult + "][详细内容:";
		break;
	case NX_IED_CTRL_SOFTSTRAP_CHECK_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.投退软压板.预校][详细内容:";
		break;
	case NX_IED_CTRL_SOFTSTRAP_CHECK_REP:			
		strDesc  = "[上送控制结果.投退软压板.预校.";
		strDesc += strResult + "][详细内容:";
		break;
	case NX_IED_CTRL_SOFTSTRAP_EXC_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.投退软压板.执行][详细内容:";
		break;
	case NX_IED_CTRL_SOFTSTRAP_EXC_REP:			
		strDesc  = "[上送控制结果.投退软压板.执行.";
		strDesc += strResult + "][详细内容:";
		break;
	case NX_IED_CTRL_SGZONE_CHECK_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.切换定值区.预校][详细内容:";
		break;	
	case NX_IED_CTRL_SGZONE_CHECK_REP:			
		strDesc  = "[上送控制结果.切换定值区.预校.";
		strDesc += strResult + "][详细内容:";
		break;
	case NX_IED_CTRL_SGZONE_EXC_ASK:
		CtrlInfoSet.bResult = true;
		strDesc = "[收到控制命令.切换定值区.执行][详细内容:";
		break;
	case NX_IED_CTRL_SGZONE_EXC_REP:
		strDesc  = "[上送控制结果.切换定值区.执行.";
		strDesc += strResult + "][详细内容:";
		break;
	default:
		CtrlInfoSet.bResult = false;
		strDesc = "[收到命令.未知][详细内容:";
		break;			
	}

	suField.str_fd_name		= "oprresult";
	suField.str_fd_value	=  CtrlInfoSet.bResult?"1":"0";
	suField.e_fd_type		= FD_TYPE_NUMERIC;
	DbOperParam.lst_fddata.push_back(suField);

	suField.str_fd_name		= "oprflag";
	suField.str_fd_value	=  "100000";
	suField.e_fd_type		= FD_TYPE_NUMERIC;
	DbOperParam.lst_fddata.push_back(suField);

	DbOperParam.lst_tablename.push_back("nx_t_usr_oprlog_ctrl");
	string strRet;
	memset(cTmp,0,512);
	char cOne[200] = "";
	for ( int i=0; i<CtrlInfoSet.vCtrlValue.size();i++)
	{		
		memset(cOne,0,200);
		CTRL_INFO * pInfo = &CtrlInfoSet.vCtrlValue[i];
		if ( (strlen(cTmp) + strlen(pInfo->cCtrlValue) + strlen(pInfo->cName))> 450 )
		{
			suField.str_fd_name	 = "log_desc";
			strRet = strDesc + cTmp + "]";
			suField.str_fd_value = strRet;
			suField.e_fd_type = FD_TYPE_CHAR;
			DbOperParam.lst_fddata.push_back(suField);
			if ( 0 != pINXEcModelMgr->dbm_insert_record(&DbOperParam,cDbInsertErr))
			{
				sprintf(cErr,"控制日志:%s 写库失败.",strRet.c_str());
				RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
			}
			strRet="";
			memset(cTmp,0,512);			
			DbOperParam.lst_fddata.pop_back();
		}		
		sprintf(cOne,"(%s%s)%s",pInfo->cName,pInfo->cCtrlValue,( i != (CtrlInfoSet.vCtrlValue.size() -1) )?",":"");
		strcat(cTmp,cOne);
	}
	if ( strlen(cTmp) > 0 || (0 == CtrlInfoSet.vCtrlValue.size()) )
	{
		suField.str_fd_name	 = "log_desc";
		strRet = strDesc + cTmp + "]";
		suField.str_fd_value = strRet;
		suField.e_fd_type = FD_TYPE_CHAR;
		DbOperParam.lst_fddata.push_back(suField);

		if ( 0 != pINXEcModelMgr->dbm_insert_record(&DbOperParam,cDbInsertErr))
		{
			sprintf(cErr,"控制日志:%s 写库失败.",strRet.c_str());
			RcdErrLogWithParentClass(cErr,"CNXEcSrvProOperation");
		}
	}
}



